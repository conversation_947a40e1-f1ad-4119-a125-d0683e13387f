using System;
using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace WordAddIn_YJZS
{
    public class ConfigurationManager
    {
        private static readonly string SettingsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");
        private static JObject _settings;
        private static readonly object _lock = new object();

        /// <summary>
        /// 获取配置设置
        /// </summary>
        private static JObject GetSettings()
        {
            if (_settings == null)
            {
                lock (_lock)
                {
                    if (_settings == null)
                    {
                        LoadSettings();
                    }
                }
            }
            return _settings;
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private static void LoadSettings()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    string json = File.ReadAllText(SettingsFilePath);
                    _settings = JObject.Parse(json);
                }
                else
                {
                    // 如果配置文件不存在，创建默认配置
                    _settings = CreateDefaultSettings();
                    SaveSettings();
                }
            }
            catch (Exception ex)
            {
                // 如果读取失败，使用默认配置
                System.Diagnostics.Debug.WriteLine($"加载配置文件失败: {ex.Message}");
                _settings = CreateDefaultSettings();
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private static JObject CreateDefaultSettings()
        {
            return JObject.Parse(@"{
                ""FilePath"": """",
                ""ModelSelection"": ""auto""
            }");
        }

        /// <summary>
        /// 保存配置文件
        /// </summary>
        private static void SaveSettings()
        {
            try
            {
                string json = _settings.ToString(Formatting.Indented);
                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存配置文件失败: {ex.Message}");
            }
        }


        /// <summary>
        /// 获取API基础地址
        /// </summary>
        /// <returns>当前环境对应的API基础地址</returns>
        public static string GetApiBaseUrl()
        {
            // 直接返回硬编码的生产环境API地址
            return "http://10.150.112.80:12010";
        }


        /// <summary>
        /// 获取文件路径配置
        /// </summary>
        public static string GetFilePath()
        {
            try
            {
                var settings = GetSettings();
                return settings["FilePath"]?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 获取模型选择配置
        /// </summary>
        public static string GetModelSelection()
        {
            try
            {
                var settings = GetSettings();
                return settings["ModelSelection"]?.ToString() ?? "auto";
            }
            catch
            {
                return "auto";
            }
        }

    }
}